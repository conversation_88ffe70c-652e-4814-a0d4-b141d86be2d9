# Spring Cloud 微服务研究实验完整运行脚本
# 自动运行基线组和优化组的对比实验

param(
    [int]$TestDuration = 300,  # 每个测试持续5分钟
    [int]$Threads = 50,        # 并发线程数
    [int]$RestInterval = 60    # 两次测试之间的休息时间
)

Write-Host "=== Spring Cloud 微服务性能优化研究实验 ===" -ForegroundColor Green
Write-Host "论文研究目标: 验证轻量级优化策略在SME环境中的有效性" -ForegroundColor Yellow
Write-Host ""
Write-Host "实验配置:" -ForegroundColor Cyan
Write-Host "- 每个测试持续时间: $TestDuration 秒 (5分钟)" -ForegroundColor White
Write-Host "- 并发线程数: $Threads" -ForegroundColor White
Write-Host "- 目标RPS: 500" -ForegroundColor White
Write-Host "- 流量分布: 70%产品查询 + 20%库存检查 + 10%订单提交" -ForegroundColor White
Write-Host "- 测试间休息: $RestInterval 秒" -ForegroundColor White

# 创建结果目录
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$experimentDir = "experiment-$timestamp"
New-Item -ItemType Directory -Path $experimentDir | Out-Null

Write-Host "`n实验结果将保存到: $experimentDir" -ForegroundColor Green

# 检查服务状态
function Test-ServicesHealth {
    Write-Host "`n检查服务健康状态..." -ForegroundColor Cyan
    
    $services = @(
        @{name="Eureka"; url="http://localhost:8761/actuator/health"},
        @{name="产品服务"; url="http://localhost:8081/actuator/health"},
        @{name="库存服务"; url="http://localhost:8082/actuator/health"},
        @{name="订单服务"; url="http://localhost:8083/actuator/health"}
    )
    
    $allHealthy = $true
    foreach ($service in $services) {
        try {
            $health = Invoke-RestMethod -Uri $service.url -TimeoutSec 5
            if ($health.status -eq "UP") {
                Write-Host "✅ $($service.name): 健康" -ForegroundColor Green
            } else {
                Write-Host "⚠️ $($service.name): $($health.status)" -ForegroundColor Yellow
                $allHealthy = $false
            }
        } catch {
            Write-Host "❌ $($service.name): 无法连接" -ForegroundColor Red
            $allHealthy = $false
        }
    }
    
    return $allHealthy
}

# 备份和修改配置文件
function Set-BaselineConfiguration {
    Write-Host "`n配置基线组 (默认30秒TTL)..." -ForegroundColor Yellow
    
    # 备份当前配置
    $services = @("product-service", "inventory-service", "order-service")
    foreach ($service in $services) {
        $configPath = "code/$service/src/main/resources/application.properties"
        $backupPath = "code/$service/src/main/resources/application.properties.optimized"
        
        if (Test-Path $configPath) {
            Copy-Item $configPath $backupPath -Force
            
            # 修改为基线配置 (30秒TTL)
            $content = Get-Content $configPath
            $content = $content -replace "eureka\.instance\.lease-renewal-interval-in-seconds=10", "eureka.instance.lease-renewal-interval-in-seconds=30"
            $content = $content -replace "eureka\.instance\.lease-expiration-duration-in-seconds=30", "eureka.instance.lease-expiration-duration-in-seconds=90"
            $content = $content -replace "eureka\.client\.registry-fetch-interval-seconds=10", "eureka.client.registry-fetch-interval-seconds=30"
            $content | Set-Content $configPath
        }
    }
    
    Write-Host "✅ 基线配置已设置 (TTL: 30秒)" -ForegroundColor Green
}

function Set-OptimizedConfiguration {
    Write-Host "`n配置优化组 (10秒TTL + CPU感知负载均衡)..." -ForegroundColor Yellow
    
    # 恢复优化配置
    $services = @("product-service", "inventory-service", "order-service")
    foreach ($service in $services) {
        $configPath = "code/$service/src/main/resources/application.properties"
        $backupPath = "code/$service/src/main/resources/application.properties.optimized"
        
        if (Test-Path $backupPath) {
            Copy-Item $backupPath $configPath -Force
        }
    }
    
    Write-Host "✅ 优化配置已设置 (TTL: 10秒, CPU感知负载均衡)" -ForegroundColor Green
}

function Restart-Services {
    Write-Host "`n重启服务以应用新配置..." -ForegroundColor Cyan
    Write-Host "请手动重启所有微服务，然后按任意键继续..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    
    # 等待服务重启完成
    Write-Host "等待服务重启完成..." -ForegroundColor Gray
    Start-Sleep -Seconds 30
    
    # 验证服务健康状态
    $maxRetries = 6
    $retryCount = 0
    
    do {
        $retryCount++
        Write-Host "验证服务状态 (尝试 $retryCount/$maxRetries)..." -ForegroundColor Gray
        $healthy = Test-ServicesHealth
        
        if (-not $healthy -and $retryCount -lt $maxRetries) {
            Write-Host "等待服务完全启动..." -ForegroundColor Gray
            Start-Sleep -Seconds 30
        }
    } while (-not $healthy -and $retryCount -lt $maxRetries)
    
    if (-not $healthy) {
        Write-Host "❌ 服务重启后健康检查失败，请检查服务状态" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ 所有服务已重启并正常运行" -ForegroundColor Green
}

# 运行性能测试
function Run-PerformanceTest {
    param($TestType, $OutputDir)
    
    Write-Host "`n=== 开始 $TestType 性能测试 ===" -ForegroundColor Magenta
    Write-Host "测试时间: $(Get-Date)" -ForegroundColor Gray
    
    # 运行数据收集脚本
    $scriptPath = ".\collect-research-data.ps1"
    $params = @{
        TestType = $TestType
        Duration = $TestDuration
        Threads = $Threads
        OutputDir = $OutputDir
    }
    
    & $scriptPath @params
    
    Write-Host "`n$TestType 测试完成" -ForegroundColor Green
}

# 生成对比报告
function Generate-ComparisonReport {
    param($ExperimentDir)
    
    Write-Host "`n生成对比分析报告..." -ForegroundColor Cyan
    
    # 查找测试结果文件
    $baselineFiles = Get-ChildItem -Path $ExperimentDir -Filter "baseline-test-*.json"
    $optimizedFiles = Get-ChildItem -Path $ExperimentDir -Filter "optimized-test-*.json"
    
    if ($baselineFiles.Count -eq 0 -or $optimizedFiles.Count -eq 0) {
        Write-Host "❌ 未找到完整的测试结果文件" -ForegroundColor Red
        return
    }
    
    $baselineResult = Get-Content $baselineFiles[0].FullName | ConvertFrom-Json
    $optimizedResult = Get-Content $optimizedFiles[0].FullName | ConvertFrom-Json
    
    # 计算改进百分比
    $latencyImprovement = if ($baselineResult.summary.actualRPS -gt 0) {
        [Math]::Round((($optimizedResult.summary.actualRPS - $baselineResult.summary.actualRPS) / $baselineResult.summary.actualRPS) * 100, 2)
    } else { 0 }
    
    $successRateImprovement = $optimizedResult.summary.overallSuccessRate - $baselineResult.summary.overallSuccessRate
    
    # 生成报告
    $reportPath = Join-Path $ExperimentDir "comparison-report.txt"
    $report = @"
=== Spring Cloud 微服务性能优化研究实验报告 ===
实验时间: $(Get-Date)

研究问题验证结果:

RQ1: Eureka TTL优化效果
- 基线组 (30秒TTL): $($baselineResult.summary.actualRPS) RPS
- 优化组 (10秒TTL): $($optimizedResult.summary.actualRPS) RPS
- 性能提升: $latencyImprovement%

RQ2: 负载均衡优化效果
- 基线组成功率: $($baselineResult.summary.overallSuccessRate)%
- 优化组成功率: $($optimizedResult.summary.overallSuccessRate)%
- 成功率提升: $successRateImprovement%

RQ3: 综合系统性能
- 吞吐量提升: $latencyImprovement%
- 成功率提升: $successRateImprovement%

详细服务对比:

产品服务 (70%流量):
- 基线组平均响应时间: $($baselineResult.services.productService.avgResponseTime)ms
- 优化组平均响应时间: $($optimizedResult.services.productService.avgResponseTime)ms

库存服务 (20%流量):
- 基线组平均响应时间: $($baselineResult.services.inventoryService.avgResponseTime)ms
- 优化组平均响应时间: $($optimizedResult.services.inventoryService.avgResponseTime)ms

订单服务 (10%流量):
- 基线组平均响应时间: $($baselineResult.services.orderService.avgResponseTime)ms
- 优化组平均响应时间: $($optimizedResult.services.orderService.avgResponseTime)ms

结论:
$(if ($latencyImprovement -ge 15) { "✅ 达到延迟降低≥15%的目标" } else { "❌ 未达到延迟降低≥15%的目标" })
$(if ($successRateImprovement -ge 20) { "✅ 达到成功率提升≥20%的目标" } else { "❌ 未达到成功率提升≥20%的目标" })

实验数据文件:
- 基线组: $($baselineFiles[0].Name)
- 优化组: $($optimizedFiles[0].Name)
"@
    
    $report | Out-File -FilePath $reportPath -Encoding UTF8
    
    Write-Host $report -ForegroundColor White
    Write-Host "`n对比报告已保存到: $reportPath" -ForegroundColor Green
}

# 主实验流程
Write-Host "`n=== 开始研究实验 ===" -ForegroundColor Magenta

# 1. 检查初始服务状态
if (-not (Test-ServicesHealth)) {
    Write-Host "❌ 服务健康检查失败，请确保所有服务正常运行" -ForegroundColor Red
    Write-Host "运行 .\start-services.ps1 启动所有服务" -ForegroundColor Yellow
    exit 1
}

# 2. 运行基线组测试
Write-Host "`n第一阶段: 基线组测试 (默认配置)" -ForegroundColor Yellow
Set-BaselineConfiguration
Restart-Services
Run-PerformanceTest -TestType "baseline" -OutputDir $experimentDir

# 3. 休息间隔
Write-Host "`n休息 $RestInterval 秒后开始优化组测试..." -ForegroundColor Gray
Start-Sleep -Seconds $RestInterval

# 4. 运行优化组测试
Write-Host "`n第二阶段: 优化组测试 (优化配置)" -ForegroundColor Yellow
Set-OptimizedConfiguration
Restart-Services
Run-PerformanceTest -TestType "optimized" -OutputDir $experimentDir

# 5. 生成对比报告
Generate-ComparisonReport -ExperimentDir $experimentDir

Write-Host "`n=== 研究实验完成 ===" -ForegroundColor Green
Write-Host "所有实验数据已保存到: $experimentDir" -ForegroundColor Cyan
Write-Host "可以使用这些数据进行论文分析和撰写" -ForegroundColor Yellow
