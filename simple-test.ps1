# Simple Service Test - Check if any services are running

Write-Host "=== Simple Service Test ===" -ForegroundColor Green
Write-Host "Checking if services are responding..." -ForegroundColor Yellow

# Test ports
$ports = @(
    @{port=8761; name="Eureka Server"},
    @{port=8081; name="Product Service"},
    @{port=8082; name="Inventory Service"},
    @{port=8083; name="Order Service"}
)

$runningServices = 0

foreach ($service in $ports) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$($service.port)" -TimeoutSec 3 -ErrorAction Stop
        Write-Host "OK $($service.name) is running on port $($service.port)" -ForegroundColor Green
        $runningServices++
    } catch {
        Write-Host "FAIL $($service.name) not responding on port $($service.port)" -ForegroundColor Red
    }
}

Write-Host "`nSummary:" -ForegroundColor Yellow
Write-Host "Running services: $runningServices / $($ports.Count)" -ForegroundColor White

if ($runningServices -gt 0) {
    Write-Host "SUCCESS: Some services are running!" -ForegroundColor Green
    
    # Test basic endpoints if services are running
    Write-Host "`nTesting basic endpoints..." -ForegroundColor Cyan
    
    # Test product service
    try {
        $productResponse = Invoke-RestMethod -Uri "http://localhost:8081/products" -TimeoutSec 3
        Write-Host "OK Product Service API: $productResponse" -ForegroundColor Green
    } catch {
        Write-Host "INFO Product Service API not ready" -ForegroundColor Gray
    }
    
    # Test inventory service
    try {
        $inventoryResponse = Invoke-RestMethod -Uri "http://localhost:8082/inventory/12345" -TimeoutSec 3
        Write-Host "OK Inventory Service API: $($inventoryResponse | ConvertTo-Json -Compress)" -ForegroundColor Green
    } catch {
        Write-Host "INFO Inventory Service API not ready" -ForegroundColor Gray
    }
    
    # Test order service
    try {
        $orderBody = @{productId="12345"; quantity=1} | ConvertTo-Json
        $orderResponse = Invoke-RestMethod -Uri "http://localhost:8083/orders" -Method POST -Body $orderBody -ContentType "application/json" -TimeoutSec 3
        Write-Host "OK Order Service API: $orderResponse" -ForegroundColor Green
    } catch {
        Write-Host "INFO Order Service API not ready" -ForegroundColor Gray
    }
    
    Write-Host "`nSystem is partially ready for testing!" -ForegroundColor Green
} else {
    Write-Host "No services are running yet. Please wait for startup to complete." -ForegroundColor Yellow
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
