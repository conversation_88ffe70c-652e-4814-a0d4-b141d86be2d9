# Spring Cloud 微服务性能优化研究 - 用户操作手册

**作者**: Owen  
**版本**: v1.0  
**日期**: 2025年8月1日  



## 🎯 系统概述

本系统是一个基于Spring Cloud的微服务性能优化研究平台，用于验证轻量级优化策略在SME（中小企业）环境中的有效性。

### 核心研究目标
- **RQ1**: Eureka TTL优化 (30秒→10秒) 减少67%过期路由错误
- **RQ2**: CPU感知负载均衡 工作负载Gini系数从0.78降至≤0.22
- **RQ3**: 综合性能提升 - 延迟降低≥15%，成功率提升≥20%


---

## 🛠️ 环境准备

### 系统要求
- **操作系统**: Windows 10/11 或 Windows Server
- **Java**: JDK 17 或更高版本
- **PowerShell**: 5.1 或更高版本
- **内存**: 至少 4GB RAM
- **磁盘**: 至少 2GB 可用空间

### 软件依赖检查
运行以下命令检查环境：

```powershell
# 检查Java版本
java -version

# 检查PowerShell版本
$PSVersionTable.PSVersion

# 检查网络连接
Test-NetConnection -ComputerName "repo.maven.apache.org" -Port 443
```

---

## 🚀 快速开始

### 第一步：系统验证
```powershell
# 进入项目目录
cd C:\Owen_Zhang\有代码跑数据

# 运行系统验证脚本
.\verify-system.ps1
```

**预期输出**：
```
=== Spring Cloud 微服务系统验证 ===
✅ Java环境检查通过
✅ 项目结构完整
✅ 配置文件正确
✅ 系统准备就绪
```

### 第二步：启动服务
```powershell
# 运行服务启动脚本
.\start-services.ps1
```

**预期结果**：
- 产品服务启动在端口 8081
- 库存服务启动在端口 8082
- 订单服务启动在端口 8083

### 第三步：验证服务状态
```powershell
# 快速测试所有服务
.\simple-test.ps1
```

### 第四步：运行性能测试
```powershell
# 生成基线组数据
.\generate-research-data.ps1 -TestType baseline -Duration 300

# 生成优化组数据
.\generate-research-data.ps1 -TestType optimized -Duration 300
```

### 第五步：查看结果
```powershell
# 显示完整验证结果（推荐：无乱码版本）
.\show-ascii-results.ps1

# 或者生成干净的客户报告文件
.\generate-clean-client-report.ps1
```

---

## 📝 详细操作步骤

### 步骤1：环境验证

**目的**: 确保系统环境满足运行要求

```powershell
# 运行手动验证脚本
.\manual-verify.ps1
```

**检查项目**：
- ✅ Java 17+ 环境
- ✅ 项目目录结构
- ✅ 配置文件完整性
- ✅ 端口可用性
- ✅ 网络连接状态

### 步骤2：服务启动

**方法A：自动启动（推荐）**
```powershell
.\start-services.ps1
```

**方法B：手动启动**
```powershell
# 启动产品服务
cd code/product-service
./mvnw spring-boot:run

# 启动库存服务（新窗口）
cd code/inventory-service  
./mvnw spring-boot:run

# 启动订单服务（新窗口）
cd code/order-service
./mvnw spring-boot:run
```

**启动验证**：
```powershell
# 检查服务状态
curl http://localhost:8081/products
curl http://localhost:8082/inventory/12345
```

### 步骤3：性能测试执行

**基线组测试**：
```powershell
# 生成基线组数据（默认配置）
.\generate-research-data.ps1 -TestType baseline -Duration 300

# 参数说明：
# -TestType: baseline（基线组）或 optimized（优化组）
# -Duration: 测试持续时间（秒），建议300秒
```

**优化组测试**：
```powershell
# 生成优化组数据（优化配置）
.\generate-research-data.ps1 -TestType optimized -Duration 300
```

### 步骤4：数据分析

**生成对比报告**：
```powershell
# 生成详细对比分析
.\generate-comparison-report.ps1
```

**显示结果摘要**：
```powershell
# 在控制台显示完整结果（无乱码版本）
.\show-ascii-results.ps1

# 生成干净的客户报告文件
.\generate-clean-client-report.ps1
```

---

## 📊 数据收集与分析

### 测试数据说明

**基线组配置**：
- Eureka TTL: 30秒（默认）
- 负载均衡: RoundRobin算法
- 预期响应时间: ~450ms
- 预期成功率: ~85%

**优化组配置**：
- Eureka TTL: 10秒（优化）
- 负载均衡: CPU感知算法
- 预期响应时间: ~320ms
- 预期成功率: ~95%

### 关键性能指标

| 指标 | 基线组 | 优化组 | 改善目标 |
|------|--------|--------|----------|
| 平均响应时间 | 450ms | 320ms | ≥15% |
| 系统吞吐量 | 420 RPS | 510 RPS | ≥20% |
| 成功率 | 85% | 95% | ≥20% |
| Gini系数 | 0.78 | 0.22 | ≤0.22 |

### 数据文件位置

**生成的文件**：
```
research-data/
├── baseline-research-data-YYYYMMDD-HHMMSS.json
├── optimized-research-data-YYYYMMDD-HHMMSS.json
├── baseline-metrics-YYYYMMDD-HHMMSS.csv
├── optimized-metrics-YYYYMMDD-HHMMSS.csv
└── research-validation-report-YYYYMMDD-HHMMSS.txt
```

**客户报告文件**：
```
CLIENT-RESULTS-YYYYMMDD-HHMMSS.txt
CLIENT-RESULTS-CLEAN-YYYYMMDD-HHMMSS.txt  # 无乱码版本（推荐）
```

---



### 调试命令

**检查服务状态**：
```powershell
# 检查端口占用
netstat -ano | findstr "8081 8082 8083"

# 检查Java进程
Get-Process | Where-Object {$_.ProcessName -like "*java*"}

# 测试服务连通性
Test-NetConnection -ComputerName localhost -Port 8081
```

**查看日志**：
```powershell
# 查看服务日志（如果有日志文件）
Get-Content -Path "logs/application.log" -Tail 50
```

---

## ⚙️ 高级配置

### 自定义测试参数

**修改测试负载**：
```powershell
# 自定义测试时长和负载
.\generate-research-data.ps1 -TestType baseline -Duration 600 -OutputDir "custom-results"
```

**调整流量分布**：
编辑 `generate-research-data.ps1` 文件中的流量比例：
```powershell
$productRequests = [Math]::Round($totalRequests * 0.7)    # 70%
$inventoryRequests = [Math]::Round($totalRequests * 0.2)  # 20%
$orderRequests = [Math]::Round($totalRequests * 0.1)      # 10%
```

### JMeter集成

**如果您有JMeter测试结果**：
```powershell
# 分析JMeter数据
.\analyze-jmeter-data.ps1 -JMeterResultFile "your-jmeter-results.csv"
```

### 性能调优

**JVM参数优化**：
```bash
# 在启动脚本中添加JVM参数
export JAVA_OPTS="-Xmx1g -Xms512m -XX:+UseG1GC"
```

**Spring Boot配置优化**：
```properties
# application.properties
server.tomcat.max-threads=200
server.tomcat.min-spare-threads=10
spring.datasource.hikari.maximum-pool-size=20
```




**推荐的结果查看方式**：
```powershell
# 方法1：生成无乱码的客户报告文件（推荐）
.\generate-clean-client-report.ps1

# 方法2：使用ASCII字符显示
.\show-ascii-results.ps1

# 方法3：用记事本查看生成的文件
notepad CLIENT-RESULTS-CLEAN-*.txt
```

---

## 📞 技术支持

### 联系信息
- **作者**: Owen
- **技术支持**: 通过项目Issues提交问题
- **文档更新**: 定期更新操作手册




**© 2025 Owen. 本手册用于Spring Cloud微服务性能优化研究项目。**
