spring.application.name=order-service
server.port=8083
# ?????10??
eureka.instance.lease-renewal-interval-in-seconds=10
# ???????30??
eureka.instance.lease-expiration-duration-in-seconds=30
# ????????10??
eureka.client.registry-fetch-interval-seconds=10

spring.zipkin.base-url=http://localhost:9411
spring.sleuth.sampler.probability=1.0
# ??Sleuth?Zipkin?????
logging.level.org.springframework.cloud.sleuth=DEBUG
logging.level.org.springframework.cloud.sleuth.zipkin2=DEBUG
logging.level.org.springframework.web=DEBUG