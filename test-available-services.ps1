# Test Available Services - Work with whatever is running

Write-Host "=== Testing Available Services ===" -ForegroundColor Green
Write-Host "Testing services that are currently running..." -ForegroundColor Yellow

# Check which services are available
$availableServices = @()

# Test Product Service
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/products" -TimeoutSec 3
    $availableServices += @{name="Product Service"; port=8081; endpoint="/products"; working=$true}
    Write-Host "OK Product Service is available" -ForegroundColor Green
} catch {
    $availableServices += @{name="Product Service"; port=8081; endpoint="/products"; working=$false}
    Write-Host "FAIL Product Service not available" -ForegroundColor Red
}

# Test Inventory Service
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8082/inventory/12345" -TimeoutSec 3
    $availableServices += @{name="Inventory Service"; port=8082; endpoint="/inventory/12345"; working=$true}
    Write-Host "OK Inventory Service is available" -ForegroundColor Green
} catch {
    $availableServices += @{name="Inventory Service"; port=8082; endpoint="/inventory/12345"; working=$false}
    Write-Host "INFO Inventory Service not yet available" -ForegroundColor Gray
}

# Test Order Service
try {
    $orderBody = @{productId="12345"; quantity=1} | ConvertTo-Json
    $response = Invoke-RestMethod -Uri "http://localhost:8083/orders" -Method POST -Body $orderBody -ContentType "application/json" -TimeoutSec 3
    $availableServices += @{name="Order Service"; port=8083; endpoint="/orders"; working=$true}
    Write-Host "OK Order Service is available" -ForegroundColor Green
} catch {
    $availableServices += @{name="Order Service"; port=8083; endpoint="/orders"; working=$false}
    Write-Host "INFO Order Service not yet available" -ForegroundColor Gray
}

# Test Eureka
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8761" -TimeoutSec 3
    Write-Host "OK Eureka Server is available" -ForegroundColor Green
    $eurekaAvailable = $true
} catch {
    Write-Host "INFO Eureka Server not yet available" -ForegroundColor Gray
    $eurekaAvailable = $false
}

$workingServices = $availableServices | Where-Object { $_.working -eq $true }

Write-Host "`nSummary:" -ForegroundColor Yellow
Write-Host "Working services: $($workingServices.Count) / $($availableServices.Count)" -ForegroundColor White

if ($workingServices.Count -gt 0) {
    Write-Host "`nRunning basic performance test on available services..." -ForegroundColor Cyan
    
    # Simple load test
    $testResults = @{}
    
    foreach ($service in $workingServices) {
        Write-Host "`nTesting $($service.name)..." -ForegroundColor Yellow
        
        $results = @{
            requests = 0
            successes = 0
            totalTime = 0
            times = @()
        }
        
        # Send 20 requests to each working service
        for ($i = 1; $i -le 20; $i++) {
            $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
            try {
                if ($service.name -eq "Order Service") {
                    $orderBody = @{productId=(Get-Random -Min 1000 -Max 9999).ToString(); quantity=1} | ConvertTo-Json
                    $response = Invoke-RestMethod -Uri "http://localhost:$($service.port)$($service.endpoint)" -Method POST -Body $orderBody -ContentType "application/json" -TimeoutSec 5
                } elseif ($service.name -eq "Inventory Service") {
                    $productId = Get-Random -Min 1000 -Max 9999
                    $response = Invoke-RestMethod -Uri "http://localhost:$($service.port)/inventory/$productId" -TimeoutSec 5
                } else {
                    $response = Invoke-RestMethod -Uri "http://localhost:$($service.port)$($service.endpoint)" -TimeoutSec 5
                }
                
                $stopwatch.Stop()
                $results.requests++
                $results.successes++
                $results.totalTime += $stopwatch.ElapsedMilliseconds
                $results.times += $stopwatch.ElapsedMilliseconds
                
                Write-Host "  Request $i : $($stopwatch.ElapsedMilliseconds)ms" -ForegroundColor Green
            } catch {
                $stopwatch.Stop()
                $results.requests++
                Write-Host "  Request $i : FAILED" -ForegroundColor Red
            }
            
            Start-Sleep -Milliseconds 100
        }
        
        # Calculate statistics
        if ($results.successes -gt 0) {
            $results.avgTime = [Math]::Round($results.totalTime / $results.successes, 2)
            $results.successRate = [Math]::Round(($results.successes / $results.requests) * 100, 2)
            $sortedTimes = $results.times | Sort-Object
            $results.minTime = $sortedTimes[0]
            $results.maxTime = $sortedTimes[-1]
            if ($sortedTimes.Count -gt 1) {
                $results.medianTime = $sortedTimes[[Math]::Floor($sortedTimes.Count / 2)]
            } else {
                $results.medianTime = $results.avgTime
            }
        } else {
            $results.avgTime = 0
            $results.successRate = 0
            $results.minTime = 0
            $results.maxTime = 0
            $results.medianTime = 0
        }
        
        $testResults[$service.name] = $results
        
        Write-Host "  Results: $($results.successes)/$($results.requests) success, Avg: $($results.avgTime)ms" -ForegroundColor Cyan
    }
    
    # Display final results
    Write-Host "`n=== Performance Test Results ===" -ForegroundColor Green
    
    foreach ($serviceName in $testResults.Keys) {
        $result = $testResults[$serviceName]
        Write-Host "`n$serviceName :" -ForegroundColor Yellow
        Write-Host "  Requests: $($result.requests)" -ForegroundColor White
        Write-Host "  Success Rate: $($result.successRate)%" -ForegroundColor White
        Write-Host "  Average Response Time: $($result.avgTime)ms" -ForegroundColor White
        Write-Host "  Median Response Time: $($result.medianTime)ms" -ForegroundColor White
        Write-Host "  Min Response Time: $($result.minTime)ms" -ForegroundColor White
        Write-Host "  Max Response Time: $($result.maxTime)ms" -ForegroundColor White
    }
    
    # Save results
    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $resultFile = "partial-test-results-$timestamp.json"
    
    $fullResults = @{
        timestamp = $timestamp
        availableServices = $workingServices.Count
        totalServices = $availableServices.Count
        eurekaAvailable = $eurekaAvailable
        testResults = $testResults
    }
    
    $fullResults | ConvertTo-Json -Depth 10 | Out-File -FilePath $resultFile -Encoding UTF8
    Write-Host "`nResults saved to: $resultFile" -ForegroundColor Green
    
    Write-Host "`nNext steps:" -ForegroundColor Yellow
    Write-Host "1. Wait for more services to start" -ForegroundColor White
    Write-Host "2. Run full system test when all services are available" -ForegroundColor White
    Write-Host "3. Use this data as baseline for comparison" -ForegroundColor White
    
} else {
    Write-Host "`nNo services are currently available for testing." -ForegroundColor Red
    Write-Host "Please wait for services to start up completely." -ForegroundColor Yellow
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
