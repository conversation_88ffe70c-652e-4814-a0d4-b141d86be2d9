package com.example.orderservice.config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClient;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.client.RestTemplate;
import com.example.orderservice.rule.CpuWeightedLoadBalancer;

@Configuration
@LoadBalancerClients({
        @LoadBalancerClient(name = "product-service", configuration = LoadBalancerConfig.class),
        @LoadBalancerClient(name = "inventory-service", configuration = LoadBalancerConfig.class)
})
@AutoConfigureAfter(LoadBalancerAutoConfiguration.class)
public class LoadBalancerConfig {

    private final ServiceInstanceListSupplier supplier;

    @Autowired
    public LoadBalancerConfig(@Lazy ServiceInstanceListSupplier supplier) {
        this.supplier = supplier;
    }

    @Bean
    public CpuWeightedLoadBalancer cpuWeightedLoadBalancer() {
        return new CpuWeightedLoadBalancer(supplier, "product-service");
    }

    @LoadBalanced
    @Bean
    public RestTemplate loadBalancedRestTemplate() {
        return new RestTemplate();
    }
}