package com.example.orderservice.controller;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@RestController
public class OrderController {
    private final RestTemplate restTemplate;

    public OrderController(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    // ❗ 使用 Map 接收 JSON 数据，提取 productId 字段
    @PostMapping(path = "/orders", consumes = MediaType.APPLICATION_JSON_VALUE)
    public String createOrder(@RequestBody Map<String, Object> order) {
        // 从 Map 中获取 productId（值可能为 Integer 或 String，需转换）
        String productId = order.get("productId").toString();

        // 调用 product-service（示例返回包含端口信息，需确保 product-service 正常）
        String productInfo = restTemplate.getForObject(
                "http://product-service/products",
                String.class
        );

        return "Order Created: Product Info - " + productInfo + ", ProductId: " + productId;
    }
}