
# Inventory Service Configuration
server.port=8082
spring.application.name=inventory-service

# Eureka Client Configuration - Optimized for Research
eureka.client.service-url.defaultZone=http://localhost:8761/eureka
# Optimized TTL: 30s -> 10s for faster service discovery
eureka.instance.lease-renewal-interval-in-seconds=10
# Service expiration time: 3x heartbeat interval
eureka.instance.lease-expiration-duration-in-seconds=30
# Client registry fetch interval optimization
eureka.client.registry-fetch-interval-seconds=10

# Monitoring and Metrics Configuration
management.endpoints.web.exposure.include=health,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true
management.metrics.tags.application=${spring.application.name}

# Distributed Tracing Configuration
spring.zipkin.base-url=http://localhost:9411
# 100% sampling for research purposes
spring.sleuth.sampler.probability=1.0

# Logging Configuration for Performance Analysis
logging.level.com.example.inventoryservice=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n