
server.port=8082
spring.application.name=inventory-service
eureka.client.service-url.defaultZone=http://localhost:8761/eureka
eureka.instance.lease-renewal-interval-in-seconds=30

# ?????30???10?
eureka.instance.lease-renewal-interval-in-seconds=10
# ???????3??????????
eureka.instance.lease-expiration-duration-in-seconds=30
# ????Eureka???????
eureka.client.registry-fetch-interval-seconds=10
# ???product-service/application.properties
management.endpoints.web.exposure.include=*
management.metrics.tags.application=${spring.application.name}
# ??Zipkin??????????
spring.zipkin.base-url=http://localhost:9411
# ??????100%????????????
spring.sleuth.sampler.probability=1.0