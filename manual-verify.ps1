# Manual Verification Script - Check if services can be started manually

Write-Host "=== Manual Service Verification ===" -ForegroundColor Green
Write-Host "This script helps verify the system step by step" -ForegroundColor Yellow

Write-Host "`nStep 1: Check if Java is available" -ForegroundColor Cyan
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "OK Java found: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "FAIL Java not found. Please install Java 17+" -ForegroundColor Red
    exit 1
}

Write-Host "`nStep 2: Check project structure" -ForegroundColor Cyan
$requiredDirs = @(
    "code/eureka-server",
    "code/product-service", 
    "code/inventory-service",
    "code/order-service"
)

foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        Write-Host "OK Found: $dir" -ForegroundColor Green
    } else {
        Write-Host "FAIL Missing: $dir" -ForegroundColor Red
    }
}

Write-Host "`nStep 3: Check if any services are already running" -ForegroundColor Cyan
$ports = @(8761, 8081, 8082, 8083)
foreach ($port in $ports) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$port" -TimeoutSec 2 -ErrorAction Stop
        Write-Host "OK Service running on port $port" -ForegroundColor Green
    } catch {
        Write-Host "INFO No service on port $port" -ForegroundColor Gray
    }
}

Write-Host "`nStep 4: Check if target directories exist (compiled code)" -ForegroundColor Cyan
$targetDirs = @(
    "code/eureka-server/build",
    "code/product-service/target",
    "code/inventory-service/target", 
    "code/order-service/target"
)

$hasCompiledCode = $false
foreach ($dir in $targetDirs) {
    if (Test-Path $dir) {
        Write-Host "OK Found compiled code: $dir" -ForegroundColor Green
        $hasCompiledCode = $true
    } else {
        Write-Host "INFO No compiled code: $dir" -ForegroundColor Gray
    }
}

Write-Host "`nStep 5: Check configuration files" -ForegroundColor Cyan
$configFiles = @(
    "code/eureka-server/src/main/resources/application.properties",
    "code/product-service/src/main/resources/application.properties",
    "code/inventory-service/src/main/resources/application.properties",
    "code/order-service/src/main/resources/application.properties"
)

foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "OK Config found: $file" -ForegroundColor Green
    } else {
        Write-Host "WARN Missing config: $file" -ForegroundColor Yellow
    }
}

Write-Host "`nStep 6: Check main application files" -ForegroundColor Cyan
$mainFiles = @(
    "code/eureka-server/src/main/java/com/example/eurekaserver/EurekaServerApplication.java",
    "code/product-service/src/main/java/com/example/productservice/ProductServiceApplication.java",
    "code/inventory-service/src/main/java/com/example/inventoryservice/InventoryServiceApplication.java",
    "code/order-service/src/main/java/com/example/orderservice/OrderServiceApplication.java"
)

foreach ($file in $mainFiles) {
    if (Test-Path $file) {
        Write-Host "OK Main class found: $file" -ForegroundColor Green
    } else {
        Write-Host "WARN Missing main class: $file" -ForegroundColor Yellow
    }
}

Write-Host "`n=== Verification Summary ===" -ForegroundColor Magenta

if ($hasCompiledCode) {
    Write-Host "GOOD: Some services have compiled code" -ForegroundColor Green
    Write-Host "`nRecommended next steps:" -ForegroundColor Yellow
    Write-Host "1. Try starting services manually using existing compiled code" -ForegroundColor White
    Write-Host "2. Or wait for Maven downloads to complete" -ForegroundColor White
} else {
    Write-Host "INFO: No compiled code found" -ForegroundColor Yellow
    Write-Host "`nRecommended next steps:" -ForegroundColor Yellow
    Write-Host "1. Wait for Maven/Gradle downloads to complete" -ForegroundColor White
    Write-Host "2. Check network connectivity" -ForegroundColor White
    Write-Host "3. Try building services one by one" -ForegroundColor White
}

Write-Host "`nCurrent Maven/Gradle processes:" -ForegroundColor Cyan
$javaProcesses = Get-Process | Where-Object { $_.ProcessName -like "*java*" -or $_.ProcessName -like "*mvn*" -or $_.ProcessName -like "*gradle*" }
if ($javaProcesses) {
    foreach ($proc in $javaProcesses) {
        Write-Host "- $($proc.ProcessName) (PID: $($proc.Id))" -ForegroundColor White
    }
} else {
    Write-Host "No Java/Maven/Gradle processes found" -ForegroundColor Gray
}

Write-Host "`nManual start commands (if needed):" -ForegroundColor Yellow
Write-Host "Eureka Server:" -ForegroundColor Cyan
Write-Host "  cd code/eureka-server && ./gradlew bootRun" -ForegroundColor White
Write-Host "Product Service:" -ForegroundColor Cyan  
Write-Host "  cd code/product-service && ./mvnw spring-boot:run" -ForegroundColor White
Write-Host "Inventory Service:" -ForegroundColor Cyan
Write-Host "  cd code/inventory-service && ./mvnw spring-boot:run" -ForegroundColor White
Write-Host "Order Service:" -ForegroundColor Cyan
Write-Host "  cd code/order-service && ./mvnw spring-boot:run" -ForegroundColor White

Write-Host "`n=== Manual Verification Complete ===" -ForegroundColor Green
