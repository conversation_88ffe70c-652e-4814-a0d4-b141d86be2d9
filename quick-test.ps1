# 快速测试脚本 - 验证系统是否准备好收集数据

Write-Host "=== Spring Cloud 微服务快速验证测试 ===" -ForegroundColor Green
Write-Host "用于验证系统是否准备好进行数据收集" -ForegroundColor Yellow

# 测试配置
$testDuration = 30  # 30秒快速测试
$requestCount = 100 # 总共发送100个请求

Write-Host "`n测试配置:" -ForegroundColor Cyan
Write-Host "- 测试时长: $testDuration 秒" -ForegroundColor White
Write-Host "- 目标请求数: $requestCount" -ForegroundColor White
Write-Host "- 流量分布: 70%产品 + 20%库存 + 10%订单" -ForegroundColor White

# 结果统计
$results = @{
    productService = @{total=0; success=0; avgTime=0; times=@()}
    inventoryService = @{total=0; success=0; avgTime=0; times=@()}
    orderService = @{total=0; success=0; avgTime=0; times=@()}
}

# 测试函数
function Test-Service {
    param($ServiceName, $Url, $Method="GET", $Body=$null)
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    try {
        if ($Method -eq "POST" -and $Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $Body -ContentType "application/json" -TimeoutSec 3
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -TimeoutSec 3
        }
        $stopwatch.Stop()
        
        $results.$ServiceName.total++
        $results.$ServiceName.success++
        $results.$ServiceName.times += $stopwatch.ElapsedMilliseconds
        
        return @{success=$true; time=$stopwatch.ElapsedMilliseconds; response=$response}
    } catch {
        $stopwatch.Stop()
        $results.$ServiceName.total++
        return @{success=$false; time=$stopwatch.ElapsedMilliseconds; error=$_.Exception.Message}
    }
}

Write-Host "`n开始快速测试..." -ForegroundColor Cyan

$startTime = Get-Date
$requestsSent = 0

while ($requestsSent -lt $requestCount -and ((Get-Date) - $startTime).TotalSeconds -lt $testDuration) {
    $random = Get-Random -Minimum 1 -Maximum 100
    
    if ($random -le 70) {
        # 70% 产品查询
        $result = Test-Service -ServiceName "productService" -Url "http://localhost:8081/products"
        if ($result.success) {
            Write-Host "✅ 产品服务: $($result.time)ms" -ForegroundColor Green
        } else {
            Write-Host "❌ 产品服务失败: $($result.error)" -ForegroundColor Red
        }
    } elseif ($random -le 90) {
        # 20% 库存检查
        $productId = Get-Random -Minimum 1000 -Maximum 9999
        $result = Test-Service -ServiceName "inventoryService" -Url "http://localhost:8082/inventory/$productId"
        if ($result.success) {
            Write-Host "✅ 库存服务: $($result.time)ms - 产品$productId" -ForegroundColor Green
        } else {
            Write-Host "❌ 库存服务失败: $($result.error)" -ForegroundColor Red
        }
    } else {
        # 10% 订单提交
        $productId = Get-Random -Minimum 1000 -Maximum 9999
        $orderBody = @{
            productId = $productId.ToString()
            quantity = Get-Random -Minimum 1 -Maximum 3
        } | ConvertTo-Json
        
        $result = Test-Service -ServiceName "orderService" -Url "http://localhost:8083/orders" -Method "POST" -Body $orderBody
        if ($result.success) {
            Write-Host "✅ 订单服务: $($result.time)ms - 订单$productId" -ForegroundColor Green
        } else {
            Write-Host "❌ 订单服务失败: $($result.error)" -ForegroundColor Red
        }
    }
    
    $requestsSent++
    
    # 控制请求频率
    Start-Sleep -Milliseconds 300
}

# 计算统计结果
foreach ($serviceName in $results.Keys) {
    $service = $results.$serviceName
    if ($service.times.Count -gt 0) {
        $service.avgTime = [Math]::Round(($service.times | Measure-Object -Average).Average, 2)
        $service.successRate = [Math]::Round(($service.success / $service.total) * 100, 2)
    } else {
        $service.avgTime = 0
        $service.successRate = 0
    }
}

# 输出测试结果
Write-Host "`n=== 快速测试结果 ===" -ForegroundColor Green

$totalRequests = ($results.Values | Measure-Object -Property total -Sum).Sum
$totalSuccesses = ($results.Values | Measure-Object -Property success -Sum).Sum
$overallSuccessRate = if ($totalRequests -gt 0) { [Math]::Round(($totalSuccesses / $totalRequests) * 100, 2) } else { 0 }

Write-Host "`n总体统计:" -ForegroundColor Yellow
Write-Host "- 总请求数: $totalRequests" -ForegroundColor White
Write-Host "- 成功请求数: $totalSuccesses" -ForegroundColor White
Write-Host "- 总体成功率: $overallSuccessRate%" -ForegroundColor White

Write-Host "`n各服务详情:" -ForegroundColor Yellow

$serviceNames = @{
    productService = "产品服务"
    inventoryService = "库存服务"  
    orderService = "订单服务"
}

foreach ($serviceName in $serviceNames.Keys) {
    $service = $results.$serviceName
    $displayName = $serviceNames.$serviceName
    
    Write-Host "`n$displayName :" -ForegroundColor Cyan
    Write-Host "  请求数: $($service.total)" -ForegroundColor White
    Write-Host "  成功数: $($service.success)" -ForegroundColor White
    Write-Host "  成功率: $($service.successRate)%" -ForegroundColor White
    Write-Host "  平均响应时间: $($service.avgTime)ms" -ForegroundColor White
}

# 检查CPU指标端点
Write-Host "`n检查CPU监控端点:" -ForegroundColor Yellow
$services = @(
    @{name="产品服务"; port=8081},
    @{name="库存服务"; port=8082},
    @{name="订单服务"; port=8083}
)

foreach ($service in $services) {
    try {
        $metricsUrl = "http://localhost:$($service.port)/actuator/metrics/process.cpu.usage"
        $cpuResponse = Invoke-RestMethod -Uri $metricsUrl -TimeoutSec 3
        $cpuUsage = [Math]::Round($cpuResponse.measurements[0].value * 100, 2)
        Write-Host "✅ $($service.name) CPU使用率: $cpuUsage%" -ForegroundColor Green
    } catch {
        Write-Host "❌ $($service.name) CPU指标获取失败" -ForegroundColor Red
    }
}

# 检查Eureka注册状态
Write-Host "`n检查Eureka服务注册:" -ForegroundColor Yellow
try {
    $eurekaApps = Invoke-RestMethod -Uri "http://localhost:8761/eureka/apps" -Headers @{Accept="application/json"} -TimeoutSec 3
    if ($eurekaApps.applications.application) {
        $registeredServices = $eurekaApps.applications.application | ForEach-Object { $_.name }
        Write-Host "✅ 已注册服务: $($registeredServices -join ', ')" -ForegroundColor Green
        
        $expectedServices = @("PRODUCT-SERVICE", "INVENTORY-SERVICE", "ORDER-SERVICE")
        $missingServices = $expectedServices | Where-Object { $_ -notin $registeredServices }
        
        if ($missingServices.Count -eq 0) {
            Write-Host "✅ 所有预期服务都已注册" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 缺少服务: $($missingServices -join ', ')" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ 未发现已注册的服务" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 无法获取Eureka注册信息" -ForegroundColor Red
}

# 系统就绪评估
Write-Host "`n=== 系统就绪评估 ===" -ForegroundColor Magenta

$readyForDataCollection = $true
$issues = @()

if ($overallSuccessRate -lt 80) {
    $readyForDataCollection = $false
    $issues += "总体成功率过低 ($overallSuccessRate%)"
}

if ($results.productService.total -eq 0) {
    $readyForDataCollection = $false
    $issues += "产品服务无响应"
}

if ($results.inventoryService.total -eq 0) {
    $readyForDataCollection = $false
    $issues += "库存服务无响应"
}

if ($results.orderService.total -eq 0) {
    $readyForDataCollection = $false
    $issues += "订单服务无响应"
}

if ($readyForDataCollection) {
    Write-Host "🎉 系统已准备好进行数据收集！" -ForegroundColor Green
    Write-Host "`n建议的下一步操作:" -ForegroundColor Yellow
    Write-Host "1. 运行完整实验: .\run-research-experiment.ps1" -ForegroundColor White
    Write-Host "2. 或运行单独测试: .\collect-research-data.ps1 -TestType optimized" -ForegroundColor White
    Write-Host "3. 或运行负载测试: .\load-test.ps1 -Duration 60" -ForegroundColor White
} else {
    Write-Host "❌ 系统尚未准备好进行数据收集" -ForegroundColor Red
    Write-Host "`n发现的问题:" -ForegroundColor Yellow
    foreach ($issue in $issues) {
        Write-Host "- $issue" -ForegroundColor Red
    }
    Write-Host "`n建议:" -ForegroundColor Yellow
    Write-Host "1. 检查所有服务是否正常启动" -ForegroundColor White
    Write-Host "2. 运行 .\start-services.ps1 重新启动服务" -ForegroundColor White
    Write-Host "3. 检查端口是否被占用" -ForegroundColor White
}

Write-Host "`n=== 快速测试完成 ===" -ForegroundColor Green
