package com.example.orderservice.rule;

import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.*;
import org.springframework.cloud.loadbalancer.core.ReactorServiceInstanceLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class CpuWeightedLoadBalancer implements ReactorServiceInstanceLoadBalancer {
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final Map<String, Double> cpuMetrics = new ConcurrentHashMap<>();
    private final RestTemplate restTemplate = new RestTemplate();
    private final String serviceId;
    private final ServiceInstanceListSupplier supplier;

    public CpuWeightedLoadBalancer(ServiceInstanceListSupplier supplier, String serviceId) {
        this.supplier = supplier;
        this.serviceId = serviceId;
        scheduler.scheduleAtFixedRate(this::updateCpuMetrics, 0, 500, TimeUnit.MILLISECONDS);
    }

    private void updateCpuMetrics() {
        supplier.get().subscribe(instances -> {
            for (ServiceInstance instance : instances) {
                try {
                    String url = "http://" + instance.getHost() + ":" + instance.getPort() + "/actuator/metrics/process.cpu.usage";
                    ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
                    double cpuUsage = (Double) ((List<Map<String, Object>>) response.getBody().get("measurements")).get(0).get("value");
                    cpuMetrics.put(instance.getInstanceId(), cpuUsage);
                } catch (Exception e) {
                    cpuMetrics.put(instance.getInstanceId(), 100.0);
                }
            }
        });
    }

    @Override
    public Mono<Response<ServiceInstance>> choose(Request request) {
        return supplier.get().next()
                .map(instances -> {
                    if (instances.isEmpty()) {
                        return new EmptyResponse();
                    }

                    ServiceInstance selected = instances.stream()
                            .filter(instance -> !cpuMetrics.containsKey(instance.getInstanceId()) || cpuMetrics.get(instance.getInstanceId()) < 100.0)
                            .min(Comparator.comparingDouble(instance -> cpuMetrics.getOrDefault(instance.getInstanceId(), 100.0)))
                            .orElse(null);

                    return selected != null ?
                            new DefaultResponse(selected) :
                            new EmptyResponse();
                });
    }
}