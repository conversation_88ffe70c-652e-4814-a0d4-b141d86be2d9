# Spring Cloud 微服务测试脚本
# 用于验证服务是否正常运行并收集基础数据

Write-Host "=== Spring Cloud 微服务测试脚本 ===" -ForegroundColor Green
Write-Host "测试目标: 验证论文中的微服务架构是否正常运行" -ForegroundColor Yellow

# 等待服务启动
Write-Host "`n等待服务启动..." -ForegroundColor Cyan
Start-Sleep -Seconds 10

# 测试Eureka服务注册中心
Write-Host "`n1. 测试Eureka服务注册中心..." -ForegroundColor Cyan
try {
    $eurekaResponse = Invoke-RestMethod -Uri "http://localhost:8761/eureka/apps" -Method GET
    Write-Host "✅ Eureka服务注册中心运行正常" -ForegroundColor Green
} catch {
    Write-Host "❌ Eureka服务注册中心连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试产品服务
Write-Host "`n2. 测试产品服务 (70%流量)..." -ForegroundColor Cyan
try {
    $productResponse = Invoke-RestMethod -Uri "http://localhost:8081/products" -Method GET
    Write-Host "✅ 产品服务响应: $productResponse" -ForegroundColor Green
} catch {
    Write-Host "❌ 产品服务连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试库存服务
Write-Host "`n3. 测试库存服务 (20%流量)..." -ForegroundColor Cyan
try {
    $inventoryResponse = Invoke-RestMethod -Uri "http://localhost:8082/inventory/12345" -Method GET
    Write-Host "✅ 库存服务响应: $($inventoryResponse | ConvertTo-Json -Compress)" -ForegroundColor Green
} catch {
    Write-Host "❌ 库存服务连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试订单服务
Write-Host "`n4. 测试订单服务 (10%流量)..." -ForegroundColor Cyan
try {
    $orderBody = @{
        productId = "12345"
        quantity = 1
    } | ConvertTo-Json
    
    $orderResponse = Invoke-RestMethod -Uri "http://localhost:8083/orders" -Method POST -Body $orderBody -ContentType "application/json"
    Write-Host "✅ 订单服务响应: $orderResponse" -ForegroundColor Green
} catch {
    Write-Host "❌ 订单服务连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试健康检查端点
Write-Host "`n5. 测试健康检查端点..." -ForegroundColor Cyan
$services = @(
    @{name="产品服务"; url="http://localhost:8081/actuator/health"},
    @{name="库存服务"; url="http://localhost:8082/actuator/health"},
    @{name="订单服务"; url="http://localhost:8083/actuator/health"}
)

foreach ($service in $services) {
    try {
        $healthResponse = Invoke-RestMethod -Uri $service.url -Method GET
        $status = $healthResponse.status
        Write-Host "✅ $($service.name) 健康状态: $status" -ForegroundColor Green
    } catch {
        Write-Host "❌ $($service.name) 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试CPU指标端点 (用于负载均衡)
Write-Host "`n6. 测试CPU指标端点..." -ForegroundColor Cyan
$metricsServices = @(
    @{name="产品服务"; url="http://localhost:8081/actuator/metrics/process.cpu.usage"},
    @{name="库存服务"; url="http://localhost:8082/actuator/metrics/process.cpu.usage"},
    @{name="订单服务"; url="http://localhost:8083/actuator/metrics/process.cpu.usage"}
)

foreach ($service in $metricsServices) {
    try {
        $metricsResponse = Invoke-RestMethod -Uri $service.url -Method GET
        $cpuUsage = $metricsResponse.measurements[0].value
        Write-Host "✅ $($service.name) CPU使用率: $([math]::Round($cpuUsage * 100, 2))%" -ForegroundColor Green
    } catch {
        Write-Host "❌ $($service.name) CPU指标获取失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "如果所有服务都显示✅，说明系统已准备好进行性能测试" -ForegroundColor Yellow
Write-Host "下一步: 运行负载测试脚本进行数据收集" -ForegroundColor Cyan
