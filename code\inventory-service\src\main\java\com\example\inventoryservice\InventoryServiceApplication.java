package com.example.inventoryservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;
import java.util.Map;
import java.util.HashMap;

@SpringBootApplication
@EnableEurekaClient
public class InventoryServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(InventoryServiceApplication.class, args);
    }

}

@RestController
class InventoryController {

    @GetMapping("/inventory/{productId}")
    public ResponseEntity<Map<String, Object>> checkInventory(@PathVariable String productId) {
        // 模拟库存检查逻辑，支持论文中20%的库存检查流量
        double availability = Math.random();
        boolean inStock = availability > 0.1; // 90%概率有库存
        int stockLevel = (int)(availability * 100);

        Map<String, Object> response = new HashMap<>();
        response.put("productId", productId);
        response.put("status", inStock ? "Available" : "Out of Stock");
        response.put("stockLevel", stockLevel);
        response.put("timestamp", System.currentTimeMillis());
        response.put("serviceInstance", "inventory-service-" + System.getProperty("server.port", "8082"));

        // 模拟一些处理时间（10-50ms）
        try {
            Thread.sleep((long)(Math.random() * 40 + 10));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        return ResponseEntity.ok(response);
    }

    @GetMapping("/inventory/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "inventory-service");
        health.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(health);
    }

    @GetMapping("/inventory")
    public ResponseEntity<Map<String, Object>> getAllInventory() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Inventory Service is running");
        response.put("totalProducts", 1000);
        response.put("availableProducts", 850);
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }
}
