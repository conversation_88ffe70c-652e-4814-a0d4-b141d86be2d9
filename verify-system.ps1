# System Verification Script for Spring Cloud Microservices Research

Write-Host "=== Spring Cloud Microservices System Verification ===" -ForegroundColor Green
Write-Host "Checking if system is ready for data collection..." -ForegroundColor Yellow

# Test configuration
$testRequests = 20
$results = @{
    productService = @{total=0; success=0; times=@()}
    inventoryService = @{total=0; success=0; times=@()}
    orderService = @{total=0; success=0; times=@()}
}

# Test function
function Test-ServiceEndpoint {
    param($ServiceName, $Url, $Method="GET", $Body=$null)
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    try {
        if ($Method -eq "POST" -and $Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $Body -ContentType "application/json" -TimeoutSec 5
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -TimeoutSec 5
        }
        $stopwatch.Stop()
        
        $results.$ServiceName.total++
        $results.$ServiceName.success++
        $results.$ServiceName.times += $stopwatch.ElapsedMilliseconds
        
        return @{success=$true; time=$stopwatch.ElapsedMilliseconds}
    } catch {
        $stopwatch.Stop()
        $results.$ServiceName.total++
        return @{success=$false; time=$stopwatch.ElapsedMilliseconds; error=$_.Exception.Message}
    }
}

Write-Host "`nTesting services..." -ForegroundColor Cyan

# Test each service
for ($i = 1; $i -le $testRequests; $i++) {
    $random = Get-Random -Minimum 1 -Maximum 100
    
    if ($random -le 70) {
        # 70% Product queries
        $result = Test-ServiceEndpoint -ServiceName "productService" -Url "http://localhost:8081/products"
        if ($result.success) {
            Write-Host "OK Product Service: $($result.time)ms" -ForegroundColor Green
        } else {
            Write-Host "FAIL Product Service: $($result.error)" -ForegroundColor Red
        }
    } elseif ($random -le 90) {
        # 20% Inventory checks
        $productId = Get-Random -Minimum 1000 -Maximum 9999
        $result = Test-ServiceEndpoint -ServiceName "inventoryService" -Url "http://localhost:8082/inventory/$productId"
        if ($result.success) {
            Write-Host "OK Inventory Service: $($result.time)ms" -ForegroundColor Green
        } else {
            Write-Host "FAIL Inventory Service: $($result.error)" -ForegroundColor Red
        }
    } else {
        # 10% Order submissions
        $productId = Get-Random -Minimum 1000 -Maximum 9999
        $orderBody = @{
            productId = $productId.ToString()
            quantity = Get-Random -Minimum 1 -Maximum 3
        } | ConvertTo-Json
        
        $result = Test-ServiceEndpoint -ServiceName "orderService" -Url "http://localhost:8083/orders" -Method "POST" -Body $orderBody
        if ($result.success) {
            Write-Host "OK Order Service: $($result.time)ms" -ForegroundColor Green
        } else {
            Write-Host "FAIL Order Service: $($result.error)" -ForegroundColor Red
        }
    }
    
    Start-Sleep -Milliseconds 200
}

# Calculate results
foreach ($serviceName in $results.Keys) {
    $service = $results.$serviceName
    if ($service.times.Count -gt 0) {
        $service.avgTime = [Math]::Round(($service.times | Measure-Object -Average).Average, 2)
        $service.successRate = [Math]::Round(($service.success / $service.total) * 100, 2)
    } else {
        $service.avgTime = 0
        $service.successRate = 0
    }
}

# Display results
Write-Host "`n=== Test Results ===" -ForegroundColor Green

$totalRequests = ($results.Values | Measure-Object -Property total -Sum).Sum
$totalSuccesses = ($results.Values | Measure-Object -Property success -Sum).Sum
$overallSuccessRate = if ($totalRequests -gt 0) { [Math]::Round(($totalSuccesses / $totalRequests) * 100, 2) } else { 0 }

Write-Host "`nOverall Statistics:" -ForegroundColor Yellow
Write-Host "- Total Requests: $totalRequests" -ForegroundColor White
Write-Host "- Successful Requests: $totalSuccesses" -ForegroundColor White
Write-Host "- Overall Success Rate: $overallSuccessRate%" -ForegroundColor White

Write-Host "`nService Details:" -ForegroundColor Yellow

$serviceNames = @{
    productService = "Product Service"
    inventoryService = "Inventory Service"  
    orderService = "Order Service"
}

foreach ($serviceName in $serviceNames.Keys) {
    $service = $results.$serviceName
    $displayName = $serviceNames.$serviceName
    
    Write-Host "`n$displayName :" -ForegroundColor Cyan
    Write-Host "  Requests: $($service.total)" -ForegroundColor White
    Write-Host "  Successes: $($service.success)" -ForegroundColor White
    Write-Host "  Success Rate: $($service.successRate)%" -ForegroundColor White
    Write-Host "  Avg Response Time: $($service.avgTime)ms" -ForegroundColor White
}

# Check CPU metrics endpoints
Write-Host "`nChecking CPU Monitoring Endpoints:" -ForegroundColor Yellow
$services = @(
    @{name="Product Service"; port=8081},
    @{name="Inventory Service"; port=8082},
    @{name="Order Service"; port=8083}
)

foreach ($service in $services) {
    try {
        $metricsUrl = "http://localhost:$($service.port)/actuator/metrics/process.cpu.usage"
        $cpuResponse = Invoke-RestMethod -Uri $metricsUrl -TimeoutSec 3
        $cpuUsage = [Math]::Round($cpuResponse.measurements[0].value * 100, 2)
        Write-Host "OK $($service.name) CPU Usage: $cpuUsage%" -ForegroundColor Green
    } catch {
        Write-Host "FAIL $($service.name) CPU metrics unavailable" -ForegroundColor Red
    }
}

# Check Eureka registration
Write-Host "`nChecking Eureka Service Registration:" -ForegroundColor Yellow
try {
    $eurekaApps = Invoke-RestMethod -Uri "http://localhost:8761/eureka/apps" -Headers @{Accept="application/json"} -TimeoutSec 3
    if ($eurekaApps.applications.application) {
        $registeredServices = $eurekaApps.applications.application | ForEach-Object { $_.name }
        Write-Host "OK Registered Services: $($registeredServices -join ', ')" -ForegroundColor Green
        
        $expectedServices = @("PRODUCT-SERVICE", "INVENTORY-SERVICE", "ORDER-SERVICE")
        $missingServices = $expectedServices | Where-Object { $_ -notin $registeredServices }
        
        if ($missingServices.Count -eq 0) {
            Write-Host "OK All expected services are registered" -ForegroundColor Green
        } else {
            Write-Host "WARNING Missing services: $($missingServices -join ', ')" -ForegroundColor Yellow
        }
    } else {
        Write-Host "WARNING No registered services found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "FAIL Cannot get Eureka registration info" -ForegroundColor Red
}

# System readiness assessment
Write-Host "`n=== System Readiness Assessment ===" -ForegroundColor Magenta

$readyForDataCollection = $true
$issues = @()

if ($overallSuccessRate -lt 80) {
    $readyForDataCollection = $false
    $issues += "Overall success rate too low ($overallSuccessRate%)"
}

if ($results.productService.total -eq 0) {
    $readyForDataCollection = $false
    $issues += "Product service not responding"
}

if ($results.inventoryService.total -eq 0) {
    $readyForDataCollection = $false
    $issues += "Inventory service not responding"
}

if ($results.orderService.total -eq 0) {
    $readyForDataCollection = $false
    $issues += "Order service not responding"
}

if ($readyForDataCollection) {
    Write-Host "SUCCESS: System is ready for data collection!" -ForegroundColor Green
    Write-Host "`nRecommended next steps:" -ForegroundColor Yellow
    Write-Host "1. Run full experiment: .\run-research-experiment.ps1" -ForegroundColor White
    Write-Host "2. Or run single test: .\collect-research-data.ps1 -TestType optimized" -ForegroundColor White
    Write-Host "3. Or run load test: .\load-test.ps1 -Duration 60" -ForegroundColor White
} else {
    Write-Host "FAIL: System is not ready for data collection" -ForegroundColor Red
    Write-Host "`nIssues found:" -ForegroundColor Yellow
    foreach ($issue in $issues) {
        Write-Host "- $issue" -ForegroundColor Red
    }
    Write-Host "`nSuggestions:" -ForegroundColor Yellow
    Write-Host "1. Check if all services are running properly" -ForegroundColor White
    Write-Host "2. Run .\start-services.ps1 to restart services" -ForegroundColor White
    Write-Host "3. Check if ports are not occupied" -ForegroundColor White
}

Write-Host "`n=== Verification Complete ===" -ForegroundColor Green
