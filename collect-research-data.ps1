# Spring Cloud 微服务研究数据收集脚本
# 用于收集论文所需的性能对比数据

param(
    [string]$TestType = "optimized",  # "baseline" 或 "optimized"
    [int]$Duration = 300,             # 测试持续时间(秒) - 5分钟
    [int]$Threads = 50,               # 并发线程数
    [string]$OutputDir = "research-data"
)

Write-Host "=== Spring Cloud 微服务研究数据收集 ===" -ForegroundColor Green
Write-Host "测试类型: $TestType" -ForegroundColor Yellow
Write-Host "持续时间: $Duration 秒" -ForegroundColor Yellow
Write-Host "并发线程: $Threads" -ForegroundColor Yellow

# 创建输出目录
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir | Out-Null
}

$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$testResultFile = Join-Path $OutputDir "$TestType-test-$timestamp.json"
$metricsFile = Join-Path $OutputDir "$TestType-metrics-$timestamp.csv"

# 初始化结果收集
$results = @{
    testConfig = @{
        testType = $TestType
        duration = $Duration
        threads = $Threads
        targetRPS = 500
        startTime = Get-Date
        timestamp = $timestamp
    }
    services = @{
        productService = @{
            requests = 0; successes = 0; failures = 0
            totalResponseTime = 0; minResponseTime = [double]::MaxValue; maxResponseTime = 0
            responseTimes = @()
        }
        inventoryService = @{
            requests = 0; successes = 0; failures = 0
            totalResponseTime = 0; minResponseTime = [double]::MaxValue; maxResponseTime = 0
            responseTimes = @()
        }
        orderService = @{
            requests = 0; successes = 0; failures = 0
            totalResponseTime = 0; minResponseTime = [double]::MaxValue; maxResponseTime = 0
            responseTimes = @()
        }
    }
    systemMetrics = @{
        cpuUsage = @()
        memoryUsage = @()
        networkLatency = @()
        eurekaRegistrations = @()
    }
}

# 测试函数
function Test-ServiceEndpoint {
    param($ServiceName, $Url, $Method = "GET", $Body = $null)
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    try {
        if ($Method -eq "POST" -and $Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $Body -ContentType "application/json" -TimeoutSec 5
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -TimeoutSec 5
        }
        
        $stopwatch.Stop()
        $responseTime = $stopwatch.ElapsedMilliseconds
        
        $service = $results.services.$ServiceName
        $service.requests++
        $service.successes++
        $service.totalResponseTime += $responseTime
        $service.minResponseTime = [Math]::Min($service.minResponseTime, $responseTime)
        $service.maxResponseTime = [Math]::Max($service.maxResponseTime, $responseTime)
        $service.responseTimes += $responseTime
        
        return @{success = $true; responseTime = $responseTime}
    } catch {
        $stopwatch.Stop()
        $results.services.$ServiceName.requests++
        $results.services.$ServiceName.failures++
        return @{success = $false; responseTime = $stopwatch.ElapsedMilliseconds; error = $_.Exception.Message}
    }
}

# 收集系统指标
function Collect-SystemMetrics {
    $timestamp = Get-Date
    
    # 收集CPU使用率
    $services = @("product-service", "inventory-service", "order-service")
    $ports = @(8081, 8082, 8083)
    
    for ($i = 0; $i -lt $services.Length; $i++) {
        try {
            $metricsUrl = "http://localhost:$($ports[$i])/actuator/metrics/process.cpu.usage"
            $cpuResponse = Invoke-RestMethod -Uri $metricsUrl -TimeoutSec 3
            $cpuUsage = $cpuResponse.measurements[0].value
            
            $results.systemMetrics.cpuUsage += @{
                timestamp = $timestamp
                service = $services[$i]
                cpuUsage = $cpuUsage
            }
        } catch {
            Write-Host "警告: 无法获取 $($services[$i]) 的CPU指标" -ForegroundColor Yellow
        }
    }
    
    # 收集Eureka注册信息
    try {
        $eurekaApps = Invoke-RestMethod -Uri "http://localhost:8761/eureka/apps" -Headers @{Accept="application/json"} -TimeoutSec 3
        $registrationCount = 0
        if ($eurekaApps.applications.application) {
            $registrationCount = ($eurekaApps.applications.application | Measure-Object).Count
        }
        
        $results.systemMetrics.eurekaRegistrations += @{
            timestamp = $timestamp
            registeredServices = $registrationCount
        }
    } catch {
        Write-Host "警告: 无法获取Eureka注册信息" -ForegroundColor Yellow
    }
}

# 工作线程函数
function Start-LoadTestWorker {
    $endTime = (Get-Date).AddSeconds($Duration)
    
    while ((Get-Date) -lt $endTime) {
        $random = Get-Random -Minimum 1 -Maximum 100
        
        if ($random -le 70) {
            # 70% 产品查询
            Test-ServiceEndpoint -ServiceName "productService" -Url "http://localhost:8081/products" | Out-Null
        } elseif ($random -le 90) {
            # 20% 库存检查
            $productId = Get-Random -Minimum 1000 -Maximum 9999
            Test-ServiceEndpoint -ServiceName "inventoryService" -Url "http://localhost:8082/inventory/$productId" | Out-Null
        } else {
            # 10% 订单提交
            $productId = Get-Random -Minimum 1000 -Maximum 9999
            $orderBody = @{
                productId = $productId.ToString()
                quantity = Get-Random -Minimum 1 -Maximum 5
            } | ConvertTo-Json
            
            Test-ServiceEndpoint -ServiceName "orderService" -Url "http://localhost:8083/orders" -Method "POST" -Body $orderBody | Out-Null
        }
        
        # 控制请求频率 (目标500 RPS / 50 threads = 10 RPS per thread)
        Start-Sleep -Milliseconds 100
    }
}

Write-Host "`n开始数据收集..." -ForegroundColor Cyan

# 启动系统指标收集
$metricsJob = Start-Job -ScriptBlock {
    param($Duration, $Results)
    $endTime = (Get-Date).AddSeconds($Duration)
    while ((Get-Date) -lt $endTime) {
        & $using:function:Collect-SystemMetrics
        Start-Sleep -Seconds 5
    }
} -ArgumentList $Duration, $results

# 启动负载测试工作线程
$jobs = @()
for ($i = 1; $i -le $Threads; $i++) {
    $job = Start-Job -ScriptBlock ${function:Start-LoadTestWorker}
    $jobs += $job
}

# 监控进度并收集实时指标
$startTime = Get-Date
$progressInterval = 10  # 每10秒显示一次进度
$lastProgressTime = $startTime

while ((Get-Date) -lt $startTime.AddSeconds($Duration)) {
    $elapsed = ((Get-Date) - $startTime).TotalSeconds
    $currentTime = Get-Date
    
    # 每10秒显示进度和收集指标
    if (($currentTime - $lastProgressTime).TotalSeconds -ge $progressInterval) {
        $progress = [Math]::Round(($elapsed / $Duration) * 100, 1)
        
        $totalRequests = $results.services.productService.requests + $results.services.inventoryService.requests + $results.services.orderService.requests
        $currentRPS = if ($elapsed -gt 0) { [Math]::Round($totalRequests / $elapsed, 1) } else { 0 }
        
        Write-Host "进度: $progress% | 已发送请求: $totalRequests | 当前RPS: $currentRPS" -ForegroundColor Yellow
        
        # 收集当前系统指标
        Collect-SystemMetrics
        
        $lastProgressTime = $currentTime
    }
    
    Start-Sleep -Seconds 1
}

Write-Host "`n等待所有线程完成..." -ForegroundColor Cyan
$jobs | Wait-Job | Out-Null
$jobs | Remove-Job

$metricsJob | Stop-Job
$metricsJob | Remove-Job

# 计算最终结果
$results.testConfig.endTime = Get-Date
$results.testConfig.actualDuration = ($results.testConfig.endTime - $results.testConfig.startTime).TotalSeconds

# 计算统计指标
foreach ($serviceName in $results.services.Keys) {
    $service = $results.services.$serviceName
    
    if ($service.successes -gt 0) {
        $service.avgResponseTime = [Math]::Round($service.totalResponseTime / $service.successes, 2)
        $service.successRate = [Math]::Round(($service.successes / $service.requests) * 100, 2)
        
        # 计算响应时间百分位数
        $sortedTimes = $service.responseTimes | Sort-Object
        $count = $sortedTimes.Count
        if ($count -gt 0) {
            $service.p50ResponseTime = $sortedTimes[[Math]::Floor($count * 0.5)]
            $service.p95ResponseTime = $sortedTimes[[Math]::Floor($count * 0.95)]
            $service.p99ResponseTime = $sortedTimes[[Math]::Floor($count * 0.99)]
        }
    } else {
        $service.avgResponseTime = 0
        $service.successRate = 0
        $service.p50ResponseTime = 0
        $service.p95ResponseTime = 0
        $service.p99ResponseTime = 0
    }
    
    if ($service.minResponseTime -eq [double]::MaxValue) {
        $service.minResponseTime = 0
    }
}

# 计算总体指标
$totalRequests = ($results.services.Values | Measure-Object -Property requests -Sum).Sum
$totalSuccesses = ($results.services.Values | Measure-Object -Property successes -Sum).Sum
$actualRPS = [Math]::Round($totalRequests / $results.testConfig.actualDuration, 2)
$overallSuccessRate = if ($totalRequests -gt 0) { [Math]::Round(($totalSuccesses / $totalRequests) * 100, 2) } else { 0 }

$results.summary = @{
    totalRequests = $totalRequests
    totalSuccesses = $totalSuccesses
    actualRPS = $actualRPS
    overallSuccessRate = $overallSuccessRate
    testType = $TestType
}

# 输出结果
Write-Host "`n=== $TestType 测试结果 ===" -ForegroundColor Green
Write-Host "总体指标:" -ForegroundColor Yellow
Write-Host "- 总请求数: $totalRequests" -ForegroundColor White
Write-Host "- 成功请求数: $totalSuccesses" -ForegroundColor White
Write-Host "- 实际RPS: $actualRPS" -ForegroundColor White
Write-Host "- 总体成功率: $overallSuccessRate%" -ForegroundColor White

Write-Host "`n各服务详细指标:" -ForegroundColor Yellow
$serviceNames = @{
    productService = "产品服务 (70%流量)"
    inventoryService = "库存服务 (20%流量)"
    orderService = "订单服务 (10%流量)"
}

foreach ($serviceName in $serviceNames.Keys) {
    $service = $results.services.$serviceName
    $displayName = $serviceNames.$serviceName
    
    Write-Host "`n$displayName :" -ForegroundColor Cyan
    Write-Host "  请求数: $($service.requests)" -ForegroundColor White
    Write-Host "  成功率: $($service.successRate)%" -ForegroundColor White
    Write-Host "  平均响应时间: $($service.avgResponseTime)ms" -ForegroundColor White
    Write-Host "  P50响应时间: $($service.p50ResponseTime)ms" -ForegroundColor White
    Write-Host "  P95响应时间: $($service.p95ResponseTime)ms" -ForegroundColor White
    Write-Host "  P99响应时间: $($service.p99ResponseTime)ms" -ForegroundColor White
}

# 保存结果
$results | ConvertTo-Json -Depth 10 | Out-File -FilePath $testResultFile -Encoding UTF8
Write-Host "`n测试结果已保存到: $testResultFile" -ForegroundColor Green

# 保存CSV格式的指标数据
$csvData = @()
foreach ($serviceName in $results.services.Keys) {
    $service = $results.services.$serviceName
    $csvData += [PSCustomObject]@{
        TestType = $TestType
        Service = $serviceName
        Requests = $service.requests
        Successes = $service.successes
        Failures = $service.failures
        SuccessRate = $service.successRate
        AvgResponseTime = $service.avgResponseTime
        P50ResponseTime = $service.p50ResponseTime
        P95ResponseTime = $service.p95ResponseTime
        P99ResponseTime = $service.p99ResponseTime
        MinResponseTime = $service.minResponseTime
        MaxResponseTime = $service.maxResponseTime
    }
}

$csvData | Export-Csv -Path $metricsFile -NoTypeInformation -Encoding UTF8
Write-Host "CSV指标数据已保存到: $metricsFile" -ForegroundColor Green

Write-Host "`n=== 数据收集完成 ===" -ForegroundColor Green
Write-Host "建议接下来运行对比测试 (baseline vs optimized)" -ForegroundColor Yellow

# 如果是优化组测试，提供RQ验证信息
if ($TestType -eq "optimized") {
    Write-Host "`n=== 研究问题验证提示 ===" -ForegroundColor Magenta
    Write-Host "RQ1 (Eureka TTL优化): 检查服务发现延迟和路由错误率" -ForegroundColor White
    Write-Host "RQ2 (CPU感知负载均衡): 检查工作负载分布的Gini系数" -ForegroundColor White
    Write-Host "RQ3 (综合性能): 对比基线组，检查延迟降低≥15%，成功率提升≥20%" -ForegroundColor White
}
