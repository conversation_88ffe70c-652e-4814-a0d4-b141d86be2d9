plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.5'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.example'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

repositories {
    mavenCentral()
}

ext {
    set('springCloudVersion', "2024.0.1")
}

dependencies {
// 在 build.gradle 中添加
    implementation 'com.netflix.eureka:eureka-client-jersey3:2.0.4'  // 确保与现有版本一致
        // 确保已包含Eureka Server依赖
        implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-server'

        // 显式添加Jersey客户端依赖（解决TransportClientFactories缺失问题）
        implementation 'com.netflix.eureka:eureka-core-jersey3:2.0.4' // 与日志中版本一致
        implementation 'org.glassfish.jersey.containers:jersey-container-servlet:3.1.10'

}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

tasks.named('test') {
    useJUnitPlatform()
}
