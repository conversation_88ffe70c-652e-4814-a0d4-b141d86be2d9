环境搭建：部署 3 个 1vCPU/512MB 的节点（模拟 SME 资源约束），通过 Docker 容器化运行 Spring Cloud 微服务（含产品、库存、订单服务）、Eureka 服务注册中心及 Spring Cloud LoadBalancer。
配置设置：
基线组：采用默认配置（Eureka TTL=30 秒，负载均衡用 RoundRobin 算法）。
优化组：Eureka 的心跳和注册中心拉取间隔设为 10 秒，启用客户端缓存；负载均衡改用 CPU 感知规则（优先选择 CPU 使用率≤70% 的健康实例，基于 Spring Boot Actuator 实时 metrics）。
负载生成：用 JMeter 模拟电商流量，包括 500 RPS 的均匀负载和 100-800 RPS 的突发负载（70% 产品查询、20% 库存检查、10% 订单提交）。
数据收集与分析：
用 Prometheus 每 5 秒收集 metrics（CPU 利用率、内存使用、Eureka 注册新鲜度、响应时间、请求成功率等），通过 Grafana 可视化。
对比两组配置的关键指标：注册信息过期率、实例负载标准差、 median 响应时间、请求成功率，验证优化效果。
验证目标：优化组需实现≥15% 的延迟降低、≥20% 的成功率提升，且负载标准差≤15%。

springcloud 维服务的调优实验数据测试可以做吗
用jmeter prometheus测
jmeter的我自己好像都快测完了但是p romethenus 测cpu的可能要改


