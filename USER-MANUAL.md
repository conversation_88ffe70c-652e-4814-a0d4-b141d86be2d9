# Spring Cloud Microservices Performance Optimization Research - User Manual

**Author**: <PERSON>  
**Version**: v1.0  
**Date**: July 31, 2025  
**Target Audience**: Researchers, Developers, Academic Users

---

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Environment Setup](#environment-setup)
3. [Quick Start](#quick-start)
4. [Detailed Operation Steps](#detailed-operation-steps)
5. [Data Collection & Analysis](#data-collection--analysis)
6. [Troubleshooting](#troubleshooting)
7. [Advanced Configuration](#advanced-configuration)

---

## 🎯 System Overview

This system is a Spring Cloud-based microservices performance optimization research platform designed to validate lightweight optimization strategies in SME (Small and Medium Enterprise) environments.

### Core Research Objectives
- **RQ1**: Eureka TTL optimization (30s→10s) reduces stale routing errors by 67%
- **RQ2**: CPU-aware load balancing reduces workload Gini coefficient from 0.78 to ≤0.22
- **RQ3**: Combined performance improvement - latency reduction ≥15%, success rate improvement ≥20%

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Product:8081    │    │ Inventory:8082  │    │ Order:8083      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Eureka:8761     │
                    └─────────────────┘
```

---

## 🛠️ Environment Setup

### System Requirements
- **OS**: Windows 10/11 or Windows Server
- **Java**: JDK 17 or higher
- **PowerShell**: 5.1 or higher
- **Memory**: At least 4GB RAM
- **Disk**: At least 2GB available space

### Dependency Check
Run the following commands to verify your environment:

```powershell
# Check Java version
java -version

# Check PowerShell version
$PSVersionTable.PSVersion

# Check network connectivity
Test-NetConnection -ComputerName "repo.maven.apache.org" -Port 443
```

---

## 🚀 Quick Start

### Step 1: System Verification
```powershell
# Navigate to project directory
cd C:\Owen_Zhang\有代码跑数据

# Run system verification script
.\verify-system.ps1
```

**Expected Output**:
```
=== Spring Cloud Microservices System Verification ===
✅ Java environment check passed
✅ Project structure complete
✅ Configuration files correct
✅ System ready
```

### Step 2: Start Services
```powershell
# Run service startup script
.\start-services.ps1
```

**Expected Results**:
- Product Service starts on port 8081
- Inventory Service starts on port 8082
- Order Service starts on port 8083

### Step 3: Verify Service Status
```powershell
# Quick test all services
.\simple-test.ps1
```

### Step 4: Run Performance Tests
```powershell
# Generate baseline group data
.\generate-research-data.ps1 -TestType baseline -Duration 300

# Generate optimized group data
.\generate-research-data.ps1 -TestType optimized -Duration 300
```

### Step 5: View Results
```powershell
# Display complete validation results
.\display-results.ps1
```

---

## 📝 Detailed Operation Steps

### Step 1: Environment Validation

**Purpose**: Ensure system environment meets running requirements

```powershell
# Run manual verification script
.\manual-verify.ps1
```

**Check Items**:
- ✅ Java 17+ environment
- ✅ Project directory structure
- ✅ Configuration file integrity
- ✅ Port availability
- ✅ Network connectivity

### Step 2: Service Startup

**Method A: Automatic Startup (Recommended)**
```powershell
.\start-services.ps1
```

**Method B: Manual Startup**
```powershell
# Start Product Service
cd code/product-service
./mvnw spring-boot:run

# Start Inventory Service (new window)
cd code/inventory-service  
./mvnw spring-boot:run

# Start Order Service (new window)
cd code/order-service
./mvnw spring-boot:run
```

**Startup Verification**:
```powershell
# Check service status
curl http://localhost:8081/products
curl http://localhost:8082/inventory/12345
```

### Step 3: Performance Test Execution

**Baseline Group Test**:
```powershell
# Generate baseline group data (default configuration)
.\generate-research-data.ps1 -TestType baseline -Duration 300

# Parameter explanation:
# -TestType: baseline or optimized
# -Duration: Test duration in seconds, recommend 300
```

**Optimized Group Test**:
```powershell
# Generate optimized group data (optimized configuration)
.\generate-research-data.ps1 -TestType optimized -Duration 300
```

### Step 4: Data Analysis

**Generate Comparison Report**:
```powershell
# Generate detailed comparison analysis
.\generate-comparison-report.ps1
```

**Display Result Summary**:
```powershell
# Display complete results in console
.\display-results.ps1
```

---

## 📊 Data Collection & Analysis

### Test Data Description

**Baseline Group Configuration**:
- Eureka TTL: 30 seconds (default)
- Load Balancing: RoundRobin algorithm
- Expected Response Time: ~450ms
- Expected Success Rate: ~85%

**Optimized Group Configuration**:
- Eureka TTL: 10 seconds (optimized)
- Load Balancing: CPU-aware algorithm
- Expected Response Time: ~320ms
- Expected Success Rate: ~95%

### Key Performance Indicators

| Metric | Baseline | Optimized | Target Improvement |
|--------|----------|-----------|-------------------|
| Avg Response Time | 450ms | 320ms | ≥15% |
| System Throughput | 420 RPS | 510 RPS | ≥20% |
| Success Rate | 85% | 95% | ≥20% |
| Gini Coefficient | 0.78 | 0.22 | ≤0.22 |

### Data File Locations

**Generated Files**:
```
research-data/
├── baseline-research-data-YYYYMMDD-HHMMSS.json
├── optimized-research-data-YYYYMMDD-HHMMSS.json
├── baseline-metrics-YYYYMMDD-HHMMSS.csv
├── optimized-metrics-YYYYMMDD-HHMMSS.csv
└── research-validation-report-YYYYMMDD-HHMMSS.txt
```

**Client Report File**:
```
CLIENT-RESULTS-YYYYMMDD-HHMMSS.txt
```

---

## 🔧 Troubleshooting

### Common Issues and Solutions

**Issue 1: Service Startup Failure**
```
Error: Port already in use
Solution: netstat -ano | findstr "8081" to find and kill the process
```

**Issue 2: Slow Maven Downloads**
```
Error: Dependency download timeout
Solution: Configure domestic Maven mirror or wait for network recovery
```

**Issue 3: Java Version Incompatibility**
```
Error: Unsupported class file major version
Solution: Upgrade to Java 17 or higher
```

**Issue 4: Out of Memory**
```
Error: OutOfMemoryError
Solution: Increase JVM memory parameters -Xmx2g
```

### Debug Commands

**Check Service Status**:
```powershell
# Check port usage
netstat -ano | findstr "8081 8082 8083"

# Check Java processes
Get-Process | Where-Object {$_.ProcessName -like "*java*"}

# Test service connectivity
Test-NetConnection -ComputerName localhost -Port 8081
```

**View Logs**:
```powershell
# View service logs (if log files exist)
Get-Content -Path "logs/application.log" -Tail 50
```

---

## ⚙️ Advanced Configuration

### Custom Test Parameters

**Modify Test Load**:
```powershell
# Custom test duration and load
.\generate-research-data.ps1 -TestType baseline -Duration 600 -OutputDir "custom-results"
```

**Adjust Traffic Distribution**:
Edit traffic ratios in `generate-research-data.ps1`:
```powershell
$productRequests = [Math]::Round($totalRequests * 0.7)    # 70%
$inventoryRequests = [Math]::Round($totalRequests * 0.2)  # 20%
$orderRequests = [Math]::Round($totalRequests * 0.1)      # 10%
```

### JMeter Integration

**If you have JMeter test results**:
```powershell
# Analyze JMeter data
.\analyze-jmeter-data.ps1 -JMeterResultFile "your-jmeter-results.csv"
```

### Performance Tuning

**JVM Parameter Optimization**:
```bash
# Add JVM parameters in startup scripts
export JAVA_OPTS="-Xmx1g -Xms512m -XX:+UseG1GC"
```

**Spring Boot Configuration Optimization**:
```properties
# application.properties
server.tomcat.max-threads=200
server.tomcat.min-spare-threads=10
spring.datasource.hikari.maximum-pool-size=20
```

---

## 📞 Technical Support

### Contact Information
- **Author**: Owen
- **Technical Support**: Submit issues through project Issues
- **Documentation Updates**: Regular manual updates

### Useful Resources
- [Spring Cloud Official Documentation](https://spring.io/projects/spring-cloud)
- [Spring Boot Actuator Guide](https://docs.spring.io/spring-boot/docs/current/reference/html/actuator.html)
- [JMeter User Manual](https://jmeter.apache.org/usermanual/index.html)

---

## 📄 Version History

| Version | Date | Updates |
|---------|------|---------|
| v1.0 | 2025-07-31 | Initial version with complete operation procedures |

---

**© 2025 Owen. This manual is for Spring Cloud Microservices Performance Optimization Research Project.**
