# Spring Cloud 负载测试脚本
# 模拟论文中的500 RPS测试场景: 70%产品查询 + 20%库存检查 + 10%订单提交

param(
    [int]$Duration = 60,  # 测试持续时间(秒)
    [int]$Threads = 50,   # 并发线程数
    [string]$OutputFile = "performance-results.json"
)

Write-Host "=== Spring Cloud 负载测试 ===" -ForegroundColor Green
Write-Host "测试配置:" -ForegroundColor Yellow
Write-Host "- 持续时间: $Duration 秒" -ForegroundColor White
Write-Host "- 并发线程: $Threads" -ForegroundColor White
Write-Host "- 流量分布: 70%产品查询 + 20%库存检查 + 10%订单提交" -ForegroundColor White
Write-Host "- 目标RPS: ~500" -ForegroundColor White

# 初始化结果收集
$results = @{
    testConfig = @{
        duration = $Duration
        threads = $Threads
        targetRPS = 500
        startTime = Get-Date
    }
    productService = @{
        requests = 0
        successes = 0
        failures = 0
        totalResponseTime = 0
        minResponseTime = [double]::MaxValue
        maxResponseTime = 0
    }
    inventoryService = @{
        requests = 0
        successes = 0
        failures = 0
        totalResponseTime = 0
        minResponseTime = [double]::MaxValue
        maxResponseTime = 0
    }
    orderService = @{
        requests = 0
        successes = 0
        failures = 0
        totalResponseTime = 0
        minResponseTime = [double]::MaxValue
        maxResponseTime = 0
    }
}

# 测试函数
function Test-ProductService {
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8081/products" -Method GET -TimeoutSec 5
        $stopwatch.Stop()
        $responseTime = $stopwatch.ElapsedMilliseconds
        
        $results.productService.requests++
        $results.productService.successes++
        $results.productService.totalResponseTime += $responseTime
        $results.productService.minResponseTime = [Math]::Min($results.productService.minResponseTime, $responseTime)
        $results.productService.maxResponseTime = [Math]::Max($results.productService.maxResponseTime, $responseTime)
        
        return $true
    } catch {
        $stopwatch.Stop()
        $results.productService.requests++
        $results.productService.failures++
        return $false
    }
}

function Test-InventoryService {
    $productId = Get-Random -Minimum 1000 -Maximum 9999
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8082/inventory/$productId" -Method GET -TimeoutSec 5
        $stopwatch.Stop()
        $responseTime = $stopwatch.ElapsedMilliseconds
        
        $results.inventoryService.requests++
        $results.inventoryService.successes++
        $results.inventoryService.totalResponseTime += $responseTime
        $results.inventoryService.minResponseTime = [Math]::Min($results.inventoryService.minResponseTime, $responseTime)
        $results.inventoryService.maxResponseTime = [Math]::Max($results.inventoryService.maxResponseTime, $responseTime)
        
        return $true
    } catch {
        $stopwatch.Stop()
        $results.inventoryService.requests++
        $results.inventoryService.failures++
        return $false
    }
}

function Test-OrderService {
    $productId = Get-Random -Minimum 1000 -Maximum 9999
    $orderBody = @{
        productId = $productId.ToString()
        quantity = Get-Random -Minimum 1 -Maximum 5
    } | ConvertTo-Json
    
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8083/orders" -Method POST -Body $orderBody -ContentType "application/json" -TimeoutSec 5
        $stopwatch.Stop()
        $responseTime = $stopwatch.ElapsedMilliseconds
        
        $results.orderService.requests++
        $results.orderService.successes++
        $results.orderService.totalResponseTime += $responseTime
        $results.orderService.minResponseTime = [Math]::Min($results.orderService.minResponseTime, $responseTime)
        $results.orderService.maxResponseTime = [Math]::Max($results.orderService.maxResponseTime, $responseTime)
        
        return $true
    } catch {
        $stopwatch.Stop()
        $results.orderService.requests++
        $results.orderService.failures++
        return $false
    }
}

# 工作线程函数
function Start-LoadTestWorker {
    $endTime = (Get-Date).AddSeconds($Duration)
    
    while ((Get-Date) -lt $endTime) {
        $random = Get-Random -Minimum 1 -Maximum 100
        
        if ($random -le 70) {
            # 70% 产品查询
            Test-ProductService | Out-Null
        } elseif ($random -le 90) {
            # 20% 库存检查
            Test-InventoryService | Out-Null
        } else {
            # 10% 订单提交
            Test-OrderService | Out-Null
        }
        
        # 控制请求频率 (目标500 RPS / 50 threads = 10 RPS per thread)
        Start-Sleep -Milliseconds 100
    }
}

Write-Host "`n开始负载测试..." -ForegroundColor Cyan

# 启动多个工作线程
$jobs = @()
for ($i = 1; $i -le $Threads; $i++) {
    $job = Start-Job -ScriptBlock ${function:Start-LoadTestWorker}
    $jobs += $job
    Write-Host "启动工作线程 $i" -ForegroundColor Gray
}

# 监控进度
$startTime = Get-Date
while ((Get-Date) -lt $startTime.AddSeconds($Duration)) {
    $elapsed = ((Get-Date) - $startTime).TotalSeconds
    $progress = [Math]::Round(($elapsed / $Duration) * 100, 1)
    
    $totalRequests = $results.productService.requests + $results.inventoryService.requests + $results.orderService.requests
    $currentRPS = if ($elapsed -gt 0) { [Math]::Round($totalRequests / $elapsed, 1) } else { 0 }
    
    Write-Host "`r进度: $progress% | 已发送请求: $totalRequests | 当前RPS: $currentRPS" -NoNewline -ForegroundColor Yellow
    Start-Sleep -Seconds 1
}

Write-Host "`n`n等待所有线程完成..." -ForegroundColor Cyan
$jobs | Wait-Job | Out-Null
$jobs | Remove-Job

# 计算最终结果
$results.testConfig.endTime = Get-Date
$results.testConfig.actualDuration = ($results.testConfig.endTime - $results.testConfig.startTime).TotalSeconds

# 计算平均响应时间
foreach ($service in @('productService', 'inventoryService', 'orderService')) {
    $serviceResults = $results.$service
    if ($serviceResults.successes -gt 0) {
        $serviceResults.avgResponseTime = [Math]::Round($serviceResults.totalResponseTime / $serviceResults.successes, 2)
        $serviceResults.successRate = [Math]::Round(($serviceResults.successes / $serviceResults.requests) * 100, 2)
    } else {
        $serviceResults.avgResponseTime = 0
        $serviceResults.successRate = 0
    }
    
    if ($serviceResults.minResponseTime -eq [double]::MaxValue) {
        $serviceResults.minResponseTime = 0
    }
}

# 计算总体指标
$totalRequests = $results.productService.requests + $results.inventoryService.requests + $results.orderService.requests
$totalSuccesses = $results.productService.successes + $results.inventoryService.successes + $results.orderService.successes
$actualRPS = [Math]::Round($totalRequests / $results.testConfig.actualDuration, 2)
$overallSuccessRate = if ($totalRequests -gt 0) { [Math]::Round(($totalSuccesses / $totalRequests) * 100, 2) } else { 0 }

$results.summary = @{
    totalRequests = $totalRequests
    totalSuccesses = $totalSuccesses
    actualRPS = $actualRPS
    overallSuccessRate = $overallSuccessRate
}

# 输出结果
Write-Host "`n=== 负载测试结果 ===" -ForegroundColor Green
Write-Host "总体指标:" -ForegroundColor Yellow
Write-Host "- 总请求数: $totalRequests" -ForegroundColor White
Write-Host "- 成功请求数: $totalSuccesses" -ForegroundColor White
Write-Host "- 实际RPS: $actualRPS" -ForegroundColor White
Write-Host "- 总体成功率: $overallSuccessRate%" -ForegroundColor White

Write-Host "`n各服务详细指标:" -ForegroundColor Yellow
$serviceNames = @{
    productService = "产品服务 (70%流量)"
    inventoryService = "库存服务 (20%流量)"
    orderService = "订单服务 (10%流量)"
}

foreach ($serviceName in $serviceNames.Keys) {
    $service = $results.$serviceName
    $displayName = $serviceNames.$serviceName
    
    Write-Host "`n$displayName :" -ForegroundColor Cyan
    Write-Host "  请求数: $($service.requests)" -ForegroundColor White
    Write-Host "  成功数: $($service.successes)" -ForegroundColor White
    Write-Host "  失败数: $($service.failures)" -ForegroundColor White
    Write-Host "  成功率: $($service.successRate)%" -ForegroundColor White
    Write-Host "  平均响应时间: $($service.avgResponseTime)ms" -ForegroundColor White
    Write-Host "  最小响应时间: $($service.minResponseTime)ms" -ForegroundColor White
    Write-Host "  最大响应时间: $($service.maxResponseTime)ms" -ForegroundColor White
}

# 保存结果到JSON文件
$results | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputFile -Encoding UTF8
Write-Host "`n结果已保存到: $OutputFile" -ForegroundColor Green

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
